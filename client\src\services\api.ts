import { supabase } from './supabaseClient';

// --- Auth Service (handled by AuthContext now) ---
// These functions are now primarily handled by AuthContext using supabase.auth

// --- Report Service ---
export const getReports = async (params: any) => {
    let query = supabase
        .from('reports')
        .select(`
            id,
            report_date,
            notes,
            users(name, branches(name)),
            report_items(quantity, unit_price)
        `);

    // Apply filters (example, needs more robust implementation)
    if (params.userId) {
        query = query.eq('user_id', params.userId);
    }
    if (params.startDate) {
        query = query.gte('report_date', params.startDate);
    }
    if (params.endDate) {
        query = query.lte('report_date', params.endDate);
    }
    // Add more filters as needed (branchId, productId)

    const { data, error } = await query;

    if (error) throw error;
    return { data }; // Wrap in { data } to match axios response structure for now
};

export const createReport = async (reportData: { items: any[]; notes: string; report_date?: string }) => {
    const { items, notes, report_date } = reportData;
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
        throw new Error('User not authenticated.');
    }

    const { data: report, error: reportError } = await supabase
        .from('reports')
        .insert({
            user_id: user.id,
            notes: notes,
            report_date: report_date || new Date().toISOString().split('T')[0],
        })
        .select()
        .single();

    if (reportError) throw reportError;

    const reportItemsToInsert = items.map(item => ({
        report_id: report.id,
        product_id: item.productId, // Ensure this is the actual product ID
        quantity: item.quantity,
        unit_price: item.unitPrice,
    }));

    const { data: reportItems, error: reportItemsError } = await supabase
        .from('report_items')
        .insert(reportItemsToInsert);

    if (reportItemsError) throw reportItemsError;

    return { report, reportItems };
};

// --- Product Service ---
export const getProducts = async (searchTerm: string) => {
    let query = supabase
        .from('products')
        .select('id, model_name');

    if (searchTerm) {
        query = query.ilike('model_name', `%${searchTerm}%`);
    }

    const { data, error } = await query;
    if (error) throw error;
    return { data };
};