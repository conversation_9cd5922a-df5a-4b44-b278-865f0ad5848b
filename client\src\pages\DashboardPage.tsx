import React from 'react';
import { useTranslation } from 'react-i18next';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, Pie<PERSON>hart, Pie, Cell } from 'recharts';

const DashboardPage: React.FC = () => {
  const { t } = useTranslation();

  // Demo data for charts
  const monthlyReportsData = [
    { month: 'يناير', reports: 45, sales: 12500 },
    { month: 'فبراير', reports: 52, sales: 15200 },
    { month: 'مارس', reports: 48, sales: 13800 },
    { month: 'أبريل', reports: 61, sales: 18900 },
    { month: 'مايو', reports: 55, sales: 16700 },
    { month: 'يونيو', reports: 67, sales: 21300 },
  ];

  const branchData = [
    { name: 'الفرع الرئيسي', value: 40, color: '#3B82F6' },
    { name: 'فرع الشمال', value: 30, color: '#10B981' },
    { name: 'فرع الجنوب', value: 20, color: '#F59E0B' },
    { name: 'فرع الشرق', value: 10, color: '#EF4444' },
  ];

  const recentReports = [
    { id: 1, employee: 'أحمد محمد', branch: 'الفرع الرئيسي', date: '2024-01-15', total: 2500 },
    { id: 2, employee: 'فاطمة أحمد', branch: 'فرع الشمال', date: '2024-01-15', total: 1800 },
    { id: 3, employee: 'محمد علي', branch: 'فرع الجنوب', date: '2024-01-14', total: 3200 },
    { id: 4, employee: 'سارة خالد', branch: 'الفرع الرئيسي', date: '2024-01-14', total: 1950 },
    { id: 5, employee: 'عبدالله سعد', branch: 'فرع الشرق', date: '2024-01-13', total: 2750 },
  ];

  const stats = [
    { title: t('total_reports'), value: '1,234', change: '+12%', color: 'bg-blue-500', icon: '📊' },
    { title: t('total_sales'), value: '₹2,45,000', change: '+8%', color: 'bg-green-500', icon: '💰' },
    { title: t('active_users'), value: '156', change: '+3%', color: 'bg-purple-500', icon: '👥' },
    { title: t('monthly_growth'), value: '18%', change: '+5%', color: 'bg-orange-500', icon: '📈' },
  ];

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('dashboard')}</h1>
        <p className="text-gray-600">مرحباً بك في لوحة التحكم - نظرة عامة على أداء التقارير اليومية</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                <p className="text-sm text-green-600 font-medium">{stat.change}</p>
              </div>
              <div className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center text-white text-xl`}>
                {stat.icon}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Monthly Reports Chart */}
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">التقارير الشهرية</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={monthlyReportsData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="reports" fill="#3B82F6" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Sales Trend Chart */}
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">اتجاه المبيعات</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={monthlyReportsData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="sales" stroke="#10B981" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Branch Distribution and Recent Reports */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Branch Distribution */}
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">توزيع الفروع</h3>
          <ResponsiveContainer width="100%" height={250}>
            <PieChart>
              <Pie
                data={branchData}
                cx="50%"
                cy="50%"
                outerRadius={80}
                dataKey="value"
                label={({ name, value }) => `${name}: ${value}%`}
              >
                {branchData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Recent Reports */}
        <div className="lg:col-span-2 bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-900">{t('recent_reports')}</h3>
            <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
              {t('view_reports')}
            </button>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-right py-2 text-sm font-medium text-gray-600">{t('employee_name')}</th>
                  <th className="text-right py-2 text-sm font-medium text-gray-600">{t('branch')}</th>
                  <th className="text-right py-2 text-sm font-medium text-gray-600">التاريخ</th>
                  <th className="text-right py-2 text-sm font-medium text-gray-600">{t('total')}</th>
                </tr>
              </thead>
              <tbody>
                {recentReports.map((report) => (
                  <tr key={report.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-3 text-sm text-gray-900">{report.employee}</td>
                    <td className="py-3 text-sm text-gray-600">{report.branch}</td>
                    <td className="py-3 text-sm text-gray-600">{report.date}</td>
                    <td className="py-3 text-sm font-medium text-gray-900">₹{report.total.toLocaleString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
