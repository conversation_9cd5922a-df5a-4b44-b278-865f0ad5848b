{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@supabase/supabase-js": "^2.50.2", "axios": "^1.10.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "jwt-decode": "^4.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.3", "react-router-dom": "^7.6.3", "recharts": "^3.0.2"}, "devDependencies": {"@eslint/js": "^9.29.0", "@tailwindcss/forms": "^0.5.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "postcss-import": "^16.1.1", "tailwindcss": "^4.1.11", "tailwindcss-rtl": "^0.9.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}