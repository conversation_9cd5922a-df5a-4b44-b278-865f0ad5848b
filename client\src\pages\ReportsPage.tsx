import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { getReports, createReport, getProducts } from '../services/api';
import { useAuth } from '../contexts/AuthContext';

const ReportsPage: React.FC = () => {
  const { t } = useTranslation();
  const { user, session } = useAuth(); // Use session to check if user is logged in
  const [reports, setReports] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Form state for new report
  const [newReportItems, setNewReportItems] = useState<any[]>([{ productId: '', quantity: 1, unitPrice: 0 }]);
  const [newReportNotes, setNewReportNotes] = useState('');
  const [productSuggestions, setProductSuggestions] = useState<any[]>([]);

  useEffect(() => {
    if (session) { // Only fetch reports if a session exists
      fetchReports();
    }
  }, [session]); // Re-fetch when session changes

  const fetchReports = async () => {
    setLoading(true);
    try {
      const { data } = await getReports({}); // No filters for now
      setReports(data);
    } catch (err: any) {
      setError('Failed to fetch reports: ' + err.message);
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleAddItem = () => {
    setNewReportItems([...newReportItems, { productId: '', quantity: 1, unitPrice: 0 }]);
  };

  const handleItemChange = (index: number, field: string, value: any) => {
    const updatedItems = [...newReportItems];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    setNewReportItems(updatedItems);
  };

  const handleProductSearch = async (searchTerm: string) => {
    if (searchTerm.length > 2) { // Only search if more than 2 characters
      try {
        const { data } = await getProducts(searchTerm);
        setProductSuggestions(data);
      } catch (err) {
        console.error('Error fetching product suggestions:', err);
        setProductSuggestions([]);
      }
    } else {
      setProductSuggestions([]);
    }
  };

  const handleNewReportSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createReport({ items: newReportItems, notes: newReportNotes });
      alert('Report submitted successfully!');
      setNewReportItems([{ productId: '', quantity: 1, unitPrice: 0 }]);
      setNewReportNotes('');
      fetchReports(); // Refresh reports list
    } catch (err: any) {
      alert('Failed to submit report: ' + err.message);
      console.error(err);
    }
  };

  return (
    <div className="p-4">
      <h1 className="text-3xl font-bold mb-6">{t('reports')}</h1>

      {user && user.user_metadata?.role === 'Employee' && (
        <div className="mb-8 p-6 bg-white rounded-lg shadow-md">
          <h2 className="text-2xl font-bold mb-4">{t('submit_daily_report')}</h2>
          <form onSubmit={handleNewReportSubmit} className="space-y-4">
            {newReportItems.map((item, index) => (
              <div key={index} className="flex flex-wrap items-center gap-4">
                <input
                  type="text"
                  placeholder={t('product_model')}
                  value={item.productId} // This should be product model name, not ID
                  onChange={(e) => {
                    handleItemChange(index, 'productId', e.target.value);
                    handleProductSearch(e.target.value);
                  }}
                  className="input input-bordered w-full md:w-1/4"
                  list={`product-suggestions-${index}`}
                  required
                />
                <datalist id={`product-suggestions-${index}`}>
                  {productSuggestions.map((product) => (
                    <option key={product.id} value={product.model_name} />
                  ))}
                </datalist>
                <input
                  type="number"
                  placeholder={t('quantity')}
                  value={item.quantity}
                  onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value))}
                  className="input input-bordered w-24"
                  min="1"
                  required
                />
                <input
                  type="number"
                  placeholder={t('unit_price')}
                  value={item.unitPrice}
                  onChange={(e) => handleItemChange(index, 'unit_price', parseFloat(e.target.value))}
                  className="input input-bordered w-24"
                  min="0"
                  step="0.01"
                  required
                />
                <span className="font-semibold">
                  {t('total')}: {(item.quantity * item.unitPrice).toFixed(2)}
                </span>
              </div>
            ))}
            <button type="button" onClick={handleAddItem} className="btn btn-outline btn-sm">
              {t('add_product')}
            </button>
            <textarea
              placeholder={t('notes_optional')}
              value={newReportNotes}
              onChange={(e) => setNewReportNotes(e.target.value)}
              className="textarea textarea-bordered w-full"
              rows={3}
            />
            <button type="submit" className="btn btn-primary w-full">
              {t('submit_report')}
            </button>
          </form>
        </div>
      )}

      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold mb-4">{t('reports')}</h2>
        {loading ? (
          <p>Loading reports...</p>
        ) : error ? (
          <p className="text-red-500">{error}</p>
        ) : reports.length === 0 ? (
          <p>{t('no_reports_found')}</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="table w-full">
              <thead>
                <tr>
                  <th>{t('employee_name')}</th>
                  <th>{t('branch')}</th>
                  <th>{t('date')}</th>
                  <th>{t('notes')}</th>
                  <th>{t('total')}</th>
                </tr>
              </thead>
              <tbody>
                {reports.map((report: any) => (
                  <tr key={report.id}>
                    <td>{report.users?.name || 'N/A'}</td>
                    <td>{report.users?.branches?.name || 'N/A'}</td>
                    <td>{new Date(report.report_date).toLocaleDateString()}</td>
                    <td>{report.notes}</td>
                    <td>
                      {report.report_items ? 
                        report.report_items.reduce((sum: number, item: any) => sum + (item.quantity * item.unit_price), 0).toFixed(2)
                        : 'N/A'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReportsPage;