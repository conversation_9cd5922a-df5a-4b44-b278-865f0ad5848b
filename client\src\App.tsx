import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import LoginPage from './pages/LoginPage';
import Navbar from './components/Navbar';
import ProtectedRoute from './components/ProtectedRoute';

// Import all pages
import ReportsPage from './pages/ReportsPage';
import DashboardPage from './pages/DashboardPage';
import CreateReportPage from './pages/CreateReportPage';
import ReportDetailsPage from './pages/ReportDetailsPage';
import ProductsPage from './pages/ProductsPage';
import UsersPage from './pages/UsersPage';
import ProfilePage from './pages/ProfilePage';
import SettingsPage from './pages/SettingsPage';

function App() {
  return (
    <Router>
      <Navbar />
      <div className="min-h-screen bg-gray-50">
        <Routes>
          {/* Public Routes */}
          <Route path="/login" element={<LoginPage />} />

          {/* Protected Routes - All Users */}
          <Route element={<ProtectedRoute allowedRoles={['Employee', 'Supervisor', 'Manager']} />}>
            <Route path="/reports" element={<ReportsPage />} />
            <Route path="/create-report" element={<CreateReportPage />} />
            <Route path="/reports/:id" element={<ReportDetailsPage />} />
            <Route path="/profile" element={<ProfilePage />} />
            <Route path="/settings" element={<SettingsPage />} />
          </Route>

          {/* Protected Routes - Supervisor and Manager */}
          <Route element={<ProtectedRoute allowedRoles={['Supervisor', 'Manager']} />}>
            <Route path="/dashboard" element={<DashboardPage />} />
            <Route path="/products" element={<ProductsPage />} />
          </Route>

          {/* Protected Routes - Manager Only */}
          <Route element={<ProtectedRoute allowedRoles={['Manager']} />}>
            <Route path="/users" element={<UsersPage />} />
          </Route>

          {/* Redirect base path to dashboard for authenticated users */}
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
