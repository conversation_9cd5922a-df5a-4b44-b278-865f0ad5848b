import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

const SettingsPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  
  const [settings, setSettings] = useState({
    language: i18n.language,
    theme: 'light',
    notifications: {
      email: true,
      push: false,
      reports: true,
      reminders: true
    },
    privacy: {
      profileVisible: true,
      activityVisible: false,
      dataSharing: false
    },
    security: {
      twoFactor: false,
      sessionTimeout: 30,
      loginAlerts: true
    },
    general: {
      autoSave: true,
      defaultView: 'dashboard',
      itemsPerPage: 10,
      dateFormat: 'dd/mm/yyyy'
    }
  });

  const handleLanguageChange = (language: string) => {
    setSettings({...settings, language});
    i18n.changeLanguage(language);
  };

  const handleNotificationChange = (key: string, value: boolean) => {
    setSettings({
      ...settings,
      notifications: {
        ...settings.notifications,
        [key]: value
      }
    });
  };

  const handlePrivacyChange = (key: string, value: boolean) => {
    setSettings({
      ...settings,
      privacy: {
        ...settings.privacy,
        [key]: value
      }
    });
  };

  const handleSecurityChange = (key: string, value: boolean | number) => {
    setSettings({
      ...settings,
      security: {
        ...settings.security,
        [key]: value
      }
    });
  };

  const handleGeneralChange = (key: string, value: any) => {
    setSettings({
      ...settings,
      general: {
        ...settings.general,
        [key]: value
      }
    });
  };

  const handleSave = () => {
    // Save settings logic here
    alert('تم حفظ الإعدادات بنجاح!');
  };

  const handleReset = () => {
    if (window.confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
      // Reset to default settings
      alert('تم إعادة تعيين الإعدادات!');
    }
  };

  const ToggleSwitch = ({ checked, onChange, label }: { checked: boolean; onChange: (value: boolean) => void; label: string }) => (
    <div className="flex items-center justify-between py-2">
      <span className="text-gray-700">{label}</span>
      <button
        onClick={() => onChange(!checked)}
        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
          checked ? 'bg-blue-600' : 'bg-gray-300'
        }`}
      >
        <span
          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
            checked ? 'translate-x-6' : 'translate-x-1'
          }`}
        />
      </button>
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('settings')}</h1>
        <p className="text-gray-600">إدارة إعدادات التطبيق وتخصيص تجربة الاستخدام</p>
      </div>

      <div className="space-y-6">
        {/* General Settings */}
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">{t('general')}</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('language')}
              </label>
              <select
                value={settings.language}
                onChange={(e) => handleLanguageChange(e.target.value)}
                className="w-full md:w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="ar">العربية</option>
                <option value="en">English</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('theme')}
              </label>
              <select
                value={settings.theme}
                onChange={(e) => setSettings({...settings, theme: e.target.value})}
                className="w-full md:w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="light">فاتح</option>
                <option value="dark">داكن</option>
                <option value="auto">تلقائي</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الصفحة الافتراضية
              </label>
              <select
                value={settings.general.defaultView}
                onChange={(e) => handleGeneralChange('defaultView', e.target.value)}
                className="w-full md:w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="dashboard">لوحة التحكم</option>
                <option value="reports">التقارير</option>
                <option value="create-report">إنشاء تقرير</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                عدد العناصر في الصفحة
              </label>
              <select
                value={settings.general.itemsPerPage}
                onChange={(e) => handleGeneralChange('itemsPerPage', parseInt(e.target.value))}
                className="w-full md:w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
            </div>

            <ToggleSwitch
              checked={settings.general.autoSave}
              onChange={(value) => handleGeneralChange('autoSave', value)}
              label="الحفظ التلقائي"
            />
          </div>
        </div>

        {/* Notifications */}
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">{t('notifications')}</h3>
          
          <div className="space-y-2">
            <ToggleSwitch
              checked={settings.notifications.email}
              onChange={(value) => handleNotificationChange('email', value)}
              label="إشعارات البريد الإلكتروني"
            />
            
            <ToggleSwitch
              checked={settings.notifications.push}
              onChange={(value) => handleNotificationChange('push', value)}
              label="الإشعارات الفورية"
            />
            
            <ToggleSwitch
              checked={settings.notifications.reports}
              onChange={(value) => handleNotificationChange('reports', value)}
              label="إشعارات التقارير الجديدة"
            />
            
            <ToggleSwitch
              checked={settings.notifications.reminders}
              onChange={(value) => handleNotificationChange('reminders', value)}
              label="تذكيرات المهام"
            />
          </div>
        </div>

        {/* Privacy */}
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">{t('privacy')}</h3>
          
          <div className="space-y-2">
            <ToggleSwitch
              checked={settings.privacy.profileVisible}
              onChange={(value) => handlePrivacyChange('profileVisible', value)}
              label="إظهار الملف الشخصي للآخرين"
            />
            
            <ToggleSwitch
              checked={settings.privacy.activityVisible}
              onChange={(value) => handlePrivacyChange('activityVisible', value)}
              label="إظهار النشاط للآخرين"
            />
            
            <ToggleSwitch
              checked={settings.privacy.dataSharing}
              onChange={(value) => handlePrivacyChange('dataSharing', value)}
              label="مشاركة البيانات لتحسين الخدمة"
            />
          </div>
        </div>

        {/* Security */}
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">{t('security')}</h3>
          
          <div className="space-y-4">
            <ToggleSwitch
              checked={settings.security.twoFactor}
              onChange={(value) => handleSecurityChange('twoFactor', value)}
              label="المصادقة الثنائية"
            />
            
            <ToggleSwitch
              checked={settings.security.loginAlerts}
              onChange={(value) => handleSecurityChange('loginAlerts', value)}
              label="تنبيهات تسجيل الدخول"
            />

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                انتهاء الجلسة (بالدقائق)
              </label>
              <select
                value={settings.security.sessionTimeout}
                onChange={(e) => handleSecurityChange('sessionTimeout', parseInt(e.target.value))}
                className="w-full md:w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value={15}>15 دقيقة</option>
                <option value={30}>30 دقيقة</option>
                <option value={60}>ساعة واحدة</option>
                <option value={120}>ساعتان</option>
                <option value={0}>لا تنتهي</option>
              </select>
            </div>
          </div>
        </div>

        {/* Data Management */}
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">إدارة البيانات</h3>
          
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">تصدير البيانات</h4>
              <p className="text-sm text-gray-600 mb-3">تحميل نسخة من جميع بياناتك</p>
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm">
                تصدير البيانات
              </button>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-2">مسح البيانات المؤقتة</h4>
              <p className="text-sm text-gray-600 mb-3">حذف الملفات المؤقتة لتحسين الأداء</p>
              <button className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm">
                مسح البيانات المؤقتة
              </button>
            </div>

            <div>
              <h4 className="font-medium text-red-600 mb-2">حذف الحساب</h4>
              <p className="text-sm text-gray-600 mb-3">حذف الحساب نهائياً مع جميع البيانات</p>
              <button className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm">
                حذف الحساب
              </button>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 space-x-reverse">
          <button
            onClick={handleReset}
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium transition-colors"
          >
            إعادة تعيين
          </button>
          <button
            onClick={handleSave}
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-medium transition-colors"
          >
            {t('save')} الإعدادات
          </button>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
