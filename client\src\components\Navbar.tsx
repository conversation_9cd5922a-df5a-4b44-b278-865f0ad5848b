import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import LanguageSwitcher from './LanguageSwitcher';

const Navbar: React.FC = () => {
  const { t } = useTranslation();
  const { isAuthenticated, logout, user } = useAuth();

  return (
    <nav className="bg-gray-800 p-4 text-white flex justify-between items-center">
      <div className="text-xl font-bold">
        <Link to="/reports">{t('app_name')}</Link>
      </div>
      <div className="flex items-center space-x-4">
        {isAuthenticated && user && (
          <span className="text-sm">{t('welcome')}, {user.name} ({user.role})</span>
        )}
        <LanguageSwitcher />
        {isAuthenticated ? (
          <button onClick={logout} className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            {t('logout')}
          </button>
        ) : (
          <Link to="/login" className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            {t('login')}
          </Link>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
