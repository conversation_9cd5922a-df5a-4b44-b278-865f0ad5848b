import React, { createContext, useState, useContext, useEffect } from 'react';

interface AuthContextType {
    session: any | null; // Demo session object
    user: any | null;    // Demo user object
    login: (email: string, password: string) => Promise<any>;
    logout: () => Promise<any>;
    isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [session, setSession] = useState<any | null>(null);
    const [user, setUser] = useState<any | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // Demo: Auto-login with demo user
        const demoUser = {
            id: 'demo-user-123',
            email: '<EMAIL>',
            name: 'مستخدم تجريبي'
        };

        const demoSession = {
            user: demoUser,
            access_token: 'demo-token'
        };

        setUser(demoUser);
        setSession(demoSession);
        setLoading(false);
    }, []);

    const login = async (email: string, password: string) => {
        // Demo login - accept any credentials
        const demoUser = {
            id: 'demo-user-123',
            email: email,
            name: 'مستخدم تجريبي'
        };

        const demoSession = {
            user: demoUser,
            access_token: 'demo-token'
        };

        setUser(demoUser);
        setSession(demoSession);

        return { user: demoUser, session: demoSession };
    };

    const logout = async () => {
        setUser(null);
        setSession(null);
    };

    const isAuthenticated = !!session;

    if (loading) {
        return <div>جاري تحميل المصادقة...</div>;
    }

    return (
        <AuthContext.Provider value={{ session, user, login, logout, isAuthenticated }}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};